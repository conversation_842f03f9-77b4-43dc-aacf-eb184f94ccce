using HotPreview;
using Xunit;

public class UIComponentAttributeTests
{
    [Fact]
    public void UIComponentAttribute_DefaultConstructor_SetsDisplayNameToNull()
    {
        // Act
        var attribute = new UIComponentAttribute();

        // Assert
        Assert.Null(attribute.DisplayName);
    }

    [Fact]
    public void UIComponentAttribute_WithDisplayName_SetsDisplayNameProperty()
    {
        // Arrange
        const string displayName = "Test Component";

        // Act
        var attribute = new UIComponentAttribute(displayName);

        // Assert
        Assert.Equal(displayName, attribute.DisplayName);
    }
}
