using System;
using System.Reflection;
using HotPreview;

// Test classes to verify AutoGeneratePreview functionality

// This class should allow auto-generation (default behavior)
[UIComponent("Test Component 1")]
public class TestComponent1
{
    public TestComponent1() { }
}

// This class should NOT allow auto-generation (explicitly disabled)
[UIComponent("Test Component 2")]
[AutoGeneratePreview(false)]
public class TestComponent2
{
    public TestComponent2() { }
}

// This class should allow auto-generation (explicitly enabled)
[UIComponent("Test Component 3")]
[AutoGeneratePreview(true)]
public class TestComponent3
{
    public TestComponent3() { }
}

// This class should allow auto-generation (default behavior, no AutoGeneratePreview attribute)
[UIComponent("Test Component 4")]
public class TestComponent4
{
    public TestComponent4() { }
}

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("Testing UIComponentAttribute and AutoGeneratePreviewAttribute functionality:");
        Console.WriteLine();

        TestAttribute<TestComponent1>("TestComponent1 (default behavior)");
        TestAttribute<TestComponent2>("TestComponent2 (explicitly disabled)");
        TestAttribute<TestComponent3>("TestComponent3 (explicitly enabled)");
        TestAttribute<TestComponent4>("TestComponent4 (default behavior)");
    }

    static void TestAttribute<T>(string description)
    {
        Type type = typeof(T);
        UIComponentAttribute? uiComponentAttribute = type.GetCustomAttribute<UIComponentAttribute>();
        AutoGeneratePreviewAttribute? autoGeneratePreviewAttribute = type.GetCustomAttribute<AutoGeneratePreviewAttribute>();
        
        Console.WriteLine($"{description}:");
        Console.WriteLine($"  DisplayName: {uiComponentAttribute?.DisplayName ?? "null"}");
        Console.WriteLine($"  AutoGeneratePreview: {autoGeneratePreviewAttribute?.AutoGenerate.ToString() ?? "null (default behavior)"}");
        Console.WriteLine();
    }
}
