using HotPreview;
using Xunit;

public class AutoGeneratePreviewAttributeTests
{
    [Fact]
    public void AutoGeneratePreviewAttribute_WithTrue_SetsAutoGenerateToTrue()
    {
        // Act
        var attribute = new AutoGeneratePreviewAttribute(true);

        // Assert
        Assert.True(attribute.AutoGenerate);
    }

    [Fact]
    public void AutoGeneratePreviewAttribute_WithFalse_SetsAutoGenerateToFalse()
    {
        // Act
        var attribute = new AutoGeneratePreviewAttribute(false);

        // Assert
        Assert.False(attribute.AutoGenerate);
    }
}
